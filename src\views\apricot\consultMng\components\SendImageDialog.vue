<template>
  <el-dialog :close-on-click-modal="false" append-to-body width="90vw" align-center @open="openDialog"
    v-model="visible" @close="closeDialog" class="my-dialog t-default">
    <template #header>
        <ul class="c-header el-dialog__title">
          <li>发送图像</li>
          <li>
            <strong class="c-bold">{{ patientInfo.sName }}</strong>
            <span>{{ patientInfo.sSexText }}</span>
            <span>{{ patientInfo.sAge }}</span>
            <span>{{ mxToDate(patientInfo.dBirthday) }}</span>
          </li>
          <li>
            <span>检查日期:</span>
            <span>{{ mxToDate(patientInfo.dAppointmentTime) }}</span>
          </li>
          <li>
            <span>核医学号:</span>
            <strong>{{ patientInfo.sNuclearNum }}</strong>
          </li>
          <li>
            <span>设备类型:</span>
            <strong>{{ patientInfo.sRoomText }}</strong>
          </li>
        </ul>
    </template>
    <div class="g-content m-flexLaout-tx">
      <div class="g-flexChild">
        <div class="m-flexLaout-ty">
          <!-- 发送节点选择 -->
          <div class="node-section">
            <el-row class="c-headline">
              <strong style="font-size: 16px;">选择发送节点</strong>
            </el-row>
            <div v-loading="nodeLoading" class="node-list">
              <el-checkbox-group v-model="selectedNodes" v-if="nodeOptions.length">
                <el-checkbox 
                  v-for="node in nodeOptions" 
                  :key="node.sId" 
                  :label="node.sId"
                  class="node-item"
                >
                  <span class="node-name">{{ node.sServerName }}</span>
                  <span class="node-detail">({{ node.sIp }}:{{ node.iPort }})</span>
                </el-checkbox>
              </el-checkbox-group>
              <el-empty v-else description="暂无可用节点" :image-size="60" />
            </div>
          </div>

          <!-- 序列信息表格 -->
          <div class="g-flexChild" style="flex:1">
            <el-row class="c-headline">
              <strong style="font-size: 16px;">选择序列</strong>
              <el-button 
                type="primary" 
                size="small" 
                @click="refreshSeries"
                :loading="seriesLoading"
                style="margin-left: 15px;"
              >
                刷新序列
              </el-button>
            </el-row>

            <div class="g-flexChild c-box relative bottom-1.5">
              <el-table 
                :data="seriesData" 
                ref="seriesTable" 
                v-loading="seriesLoading" 
                stripe 
                border 
                height="100%"
                @selection-change="handleSeriesSelectionChange" 
                style="width: 100%"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column align="center" label="序号" type="index" width="60" />
                <el-table-column prop="seriesNumber" label="序列号" width="80" align="center" />
                <el-table-column prop="seriesDescription" label="序列描述" min-width="150" show-overflow-tooltip />
                <el-table-column prop="modality" label="设备类型" width="100" align="center" />
                <el-table-column prop="seriesDate" label="序列日期" width="120" align="center" />
                <el-table-column prop="seriesTime" label="序列时间" width="120" align="center" />
                <el-table-column prop="numberOfSeriesRelatedInstances" label="图像数量" width="100" align="center" />
                <el-table-column prop="bodyPartExamined" label="检查部位" width="120" align="center" />
              </el-table>
              
              <el-empty v-if="!seriesData.length && !seriesLoading" description="暂无序列数据" :image-size="80" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作区 -->
    <template #footer>
      <div class="dialog-footer">
        <div class="send-stats" v-if="selectedSeries.length && selectedNodes.length">
          <span>已选择 {{ selectedSeries.length }} 个序列，{{ selectedNodes.length }} 个节点</span>
          <span class="stats-detail">将发送 {{ selectedSeries.length * selectedNodes.length }} 个任务</span>
        </div>
        <div class="footer-buttons">
          <el-button @click="closeDialog">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleSendImage"
            :loading="sending"
            :disabled="!selectedSeries.length || !selectedNodes.length"
          >
            发送图像
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { deepClone } from '$supersetUtils/function'
import { getStoreNameByRoute } from '$supersetResource/js/projects/apricot/index.js'
import Api from '$supersetApi/projects/apricot/case/report.js'
import { getDicomNode } from '$supersetApi/projects/apricot/system/dicomSet.js'

export default {
  name: 'SendImageDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    moduleName: {
      type: String,
      default: ''
    }
  },
  emits: ['update:dialogVisible'],
  data() {
    return {
      visible: false,
      iModule: '',
      nodeLoading: false,
      seriesLoading: false,
      sending: false,
      
      // 节点相关
      nodeOptions: [],
      selectedNodes: [],
      
      // 序列相关
      seriesData: [],
      selectedSeries: [],
      studyList: []
    }
  },
  computed: {
    userInfo() {
      let temp = this.$store.getters['user/userSystemInfo']
      if (temp.__proto__.constructor === Object) {
        return temp
      } else {
        return {}
      }
    },
    patientInfo() {
      if (this.$store.state.apricot[this.iModule]) {
        let patientInfo = this.$store.state.apricot[this.iModule].patientInfo || {}
        return patientInfo;
      }
      return {};
    }
  },
  watch: {
    dialogVisible() {
      this.visible = this.dialogVisible;
    }
  },
  methods: {
    // 初始化对话框
    async openDialog() {
      this.selectedNodes = []
      this.selectedSeries = []
      this.seriesData = []
      this.studyList = []
      
      if (!this.patientInfo.sId) {
        this.$message.warning('请先选择患者')
        this.closeDialog()
        return
      }
      
      // 获取节点数据
      await this.getDicomNodeData()
      
      // 获取序列数据
      await this.getSeriesData()
    },

    // 获取DICOM节点数据
    async getDicomNodeData() {
      this.nodeLoading = true
      try {
        const res = await getDicomNode()
        if (res.success) {
          this.nodeOptions = res.data || []
        } else {
          this.$message.error('获取节点数据失败：' + res.msg)
        }
      } catch (error) {
        console.error('获取节点数据失败:', error)
        this.$message.error('获取节点数据失败')
      } finally {
        this.nodeLoading = false
      }
    },

    // 获取序列数据
    async getSeriesData() {
      if (!this.patientInfo.sId) return
      
      this.seriesLoading = true
      try {
        // 先获取Study信息
        const studyRes = await Api.findStudyByPatientInfoId({
          sPatientInfoId: this.patientInfo.sId,
          iIsRebuid: null
        })
        
        if (studyRes.success && studyRes.data && studyRes.data.length > 0) {
          this.studyList = studyRes.data
          
          // 获取所有Study的序列信息
          let allSeries = []
          for (const study of studyRes.data) {
            try {
              const seriesRes = await Api.findStudySeries({
                studyInstanceUid: study.studyInstanceUid
              })
              
              if (seriesRes.success && seriesRes.data) {
                allSeries = allSeries.concat(seriesRes.data)
              }
            } catch (error) {
              console.error('获取序列数据失败:', error)
            }
          }
          
          this.seriesData = allSeries
        } else {
          this.$message.warning('该患者暂无图像数据')
          this.seriesData = []
        }
      } catch (error) {
        console.error('获取序列数据失败:', error)
        this.$message.error('获取序列数据失败')
      } finally {
        this.seriesLoading = false
      }
    },

    // 刷新序列数据
    refreshSeries() {
      this.getSeriesData()
    },

    // 处理序列选择变化
    handleSeriesSelectionChange(selection) {
      this.selectedSeries = selection
    },

    // 发送图像
    async handleSendImage() {
      if (!this.selectedSeries.length) {
        this.$message.warning('请选择要发送的序列')
        return
      }
      
      if (!this.selectedNodes.length) {
        this.$message.warning('请选择发送节点')
        return
      }
      
      this.sending = true
      let successCount = 0
      let totalTasks = this.selectedSeries.length * this.selectedNodes.length
      
      const loading = this.$loading({
        lock: true,
        text: '正在发送图像...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      try {
        // 获取选中的节点信息
        const selectedNodeData = this.nodeOptions.filter(node => 
          this.selectedNodes.includes(node.sId)
        )
        
        // 构建序列UID列表
        const seriesInstanceUidList = this.selectedSeries.map(series => series.seriesInstanceUid)
        
        // 向每个节点发送序列
        for (const node of selectedNodeData) {
          try {
            const sendData = {
              sAETitle: node.sAETitle,
              sIp: node.sIp,
              iPort: node.iPort,
              seriesInstanceUidList: seriesInstanceUidList
            }
            
            const res = await Api.imgPatientDicomSend(sendData)
            
            if (res.success) {
              successCount += this.selectedSeries.length
              this.$message.success(`${node.sServerName} 节点发送成功`)
            } else {
              this.$message.error(`${node.sServerName} 节点发送失败：${res.msg}`)
            }
          } catch (error) {
            console.error(`发送到节点 ${node.sServerName} 失败:`, error)
            this.$message.error(`发送到节点 ${node.sServerName} 失败`)
          }
        }
        
        if (successCount === totalTasks) {
          this.$message.success('所有图像发送完成！')
          this.closeDialog()
        } else if (successCount > 0) {
          this.$message.warning(`部分图像发送成功 (${successCount}/${totalTasks})`)
        } else {
          this.$message.error('图像发送失败')
        }
        
      } catch (error) {
        console.error('发送图像失败:', error)
        this.$message.error('发送图像失败')
      } finally {
        loading.close()
        this.sending = false
      }
    },

    // 关闭对话框
    closeDialog() {
      this.visible = false
      this.$emit('update:dialogVisible', false)
    }
  },
  created() {
    this.iModule = getStoreNameByRoute(this.$route.name);
  }
}
</script>

<style lang="scss" scoped>
.c-header {
  padding: 0px;
  display: flex;
  font-size: 16px;
  margin: 0;

  li {
    margin-right: 20px;
    list-style-type: none;

    span:not(:last-child), strong:not(:last-child) {
      padding-right: 8px;
    }
  }
}

.g-content {
  height: 75vh;

  .g-flexChild {
    overflow: hidden;
  }

  .c-headline {
    color: var(--el-color-primary);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
  }
}

.node-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fafafa;

  .node-list {
    margin-top: 10px;

    .node-item {
      display: inline-block;
      margin-right: 20px;
      margin-bottom: 10px;

      .node-name {
        font-weight: 500;
        color: #303133;
      }

      .node-detail {
        color: #909399;
        font-size: 12px;
        margin-left: 5px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .send-stats {
    flex: 1;
    text-align: left;
    color: #606266;

    .stats-detail {
      color: #409EFF;
      font-weight: 500;
      margin-left: 10px;
    }
  }

  .footer-buttons {
    display: flex;
    gap: 10px;
  }
}

:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
}

:deep(.el-table td) {
  padding: 8px 0;
}

:deep(.el-pagination) {
  padding-right: 0;
}
</style>
