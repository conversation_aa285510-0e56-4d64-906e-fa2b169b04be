<template>
  <LayoutTable>
    <template v-slot:header>
      <!-- 搜索 -->
      <SearchList v-model:modelValue="condition" v-model:list="searchListConfig" :optionData="optionsLoc"
        :iModuleId="iModuleId" storageKey="ReportCaseIndexSearch" @changeSearch="mxDoSearch"
        @reset="handleResetCondition()">
        <template v-slot:dAppointmentTimeSt>
          <el-date-picker v-model="condition.dAppointmentTimeSt" type="date"
            :picker-options="pickerOptionsAppointDayStart" @change="changeAppointTime" style="height:100%;">
          </el-date-picker>
        </template>
        <template v-slot:dAppointmentTimeEd>
          <el-date-picker v-model="condition.dAppointmentTimeEd" type="date" 
            @change="changeAppointTime" style="height:100%;">
          </el-date-picker>
        </template>
        <template v-slot:sRecentDays>
          <el-select v-model="condition.sRecentDays" @change="changeTimes" placeholder="" clearable>
            <el-option v-for="(item, index) in optionsLoc.recentDayOptions" :key="index" :label="item.sName"
              :value="item.keyWord">
            </el-option>
          </el-select>
        </template>
        <template v-slot:sDistrictId>
          <el-select v-model="condition.sDistrictId" placeholder="" @change="onChangeHospital" clearable>
            <el-option v-for="(item, index) in optionsLoc.districtArrOption" 
              :key="index" :label="item.sDistrictPrefix" :value="item.sId">
            </el-option>
          </el-select>
        </template>
        <template v-slot:sMachineryRoomId>
          <el-select v-model="condition.sMachineryRoomId" @change="onChangeMachineRoom" placeholder="" clearable
            class="no-wrap-select" multiple collapse-tags collapse-tags-tooltip>
            <el-option v-for="(item, index) in optionsLoc.machineRoomArrOption" 
              :key="index" :label="item.sRoomName" :value="item.sId">
            </el-option>
          </el-select>
        </template>
        <template v-slot:sProjectId>
          <el-select v-model="condition.sProjectId" placeholder="" @change="mxDoSearch" clearable 
            class="no-wrap-select" multiple collapse-tags collapse-tags-tooltip>
            <el-option v-for="(item, index) in optionsLoc.itemsArrOption" 
              :key="index" :label="item.sItemName" :value="item.sId">
              <span style="float: left">{{ item.sItemName }}</span>
              <span style="float: right; right: 5px; color: #8492a6; font-size: 13px">{{ item.sDeviceTypeName }}</span>
            </el-option>
          </el-select>
        </template>
        <template v-slot:dInjectTime>
          <el-date-picker v-model="condition['dInjectTime']" type="date" :picker-options="pickerOptionsAppointDayStart"
            @change="changeInjectTime">
          </el-date-picker>
        </template>
        <template v-for="sProp in ['iConsultation', 'iIsReportCommit', 'iIsApprove', 'iIsPrint', 'iIsImaging', 'iIsFinalApprove']"
          :key="sProp" v-slot:[sProp]>
          <el-select v-model="condition[sProp]" @change="mxDoSearch" placeholder="" clearable>
            <el-option v-for="(item, index) in optionsLoc[`${sProp}Options`]" :key="index" :label="item.sName"
              :value="item.sValue">
            </el-option>
          </el-select>
        </template>
        <template v-slot:sPositionText>
          <el-select v-model="condition['sPositionText']" placeholder="" clearable filterable allow-create
            default-first-option style="width:100%" @change="mxDoSearch">
            <el-option v-for="item in optionsLoc.positionOptions" :key="item.sId" :label="item.sItemPositionName"
              :value="item.sItemPositionName"></el-option>
          </el-select>
        </template>
        <template v-slot:sNuclideText>
          <el-select v-model="condition['sNuclideText']" placeholder="" clearable filterable allow-create
            default-first-option style="width:100%" @change="mxDoSearch">
            <el-option v-for="item in optionsLoc.nuclideOptions" :key="item.sId" :label="item.sNuclideName"
              :value="item.sNuclideName">
              <span style="float: left">{{ item.sNuclideName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px" v-html="item.sNuclideSupName"></span>
            </el-option>
          </el-select>
        </template>
        <template v-slot:sTracerText>
          <el-select v-model="condition.sTracerTextList" placeholder="" clearable 
            style="width:100%" @change="mxDoSearch" filterable multiple collapse-tags collapse-tags-tooltip>
            <el-option v-for="item in optionsLoc.tracerOptions" :key="item.sId" :label="item.sTracerName"
              :value="item.sTracerName">
              <span style="float: left">{{ item.sTracerName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px" v-html="item.sTracerSupName"></span>
            </el-option>
          </el-select>
        </template>
        <template v-slot:sQualitative>
          <el-select v-model="condition.sQualitative" @change="mxDoSearch" placeholder="" clearable>
            <el-option v-for="(item, index) in optionsLoc.qualitative" :key="index" :label="item.sName"
              :value="item.sValue">
            </el-option>
          </el-select>
        </template>
        <template v-for="sProp in ['sPracticeName', 'sReporterName', 'sApproveName']" v-slot:[sProp]>
          <el-select v-model="condition[sProp]" placeholder="" clearable filterable allow-create default-first-option
            style="width:100%" @change="mxDoSearch">
            <el-option v-for="item in optionsLoc[`${sProp}Options`]" :key="item.sId" :label="item.userName"
              :value="item.userName">
            </el-option>
          </el-select>
        </template>
        <template v-slot:sFinalExamineName>
          <el-select v-model="condition.sFinalExamineName" placeholder="" clearable filterable allow-create
            default-first-option style="width:100%" @change="mxDoSearch">
            <el-option v-for="(item, index) in optionsLoc[`${'sFinalExamineName'}Options`]" :key="index"
              :label="item.sName" :value="item.sName">
            </el-option>
          </el-select>
        </template>
        <template v-slot:sApplyDepartText>
          <el-select v-model="condition.sApplyDepartText" placeholder="" clearable filterable allow-create
            default-first-option style="width:100%" @change="mxDoSearch">
            <el-option v-for="(item, index) in optionsLoc[`sApplyDepartTextOptions`]" :key="index"
              :label="item.sApplyDeptName" :value="item.sApplyDeptName">
            </el-option>
          </el-select>
        </template>
        <template v-slot:sApplyPersonName>
          <el-select v-model="condition.sApplyPersonName" placeholder="" clearable filterable allow-create
            default-first-option style="width:100%" @change="mxDoSearch">
            <el-option v-for="(item, index) in optionsLoc[`sApplyPersonNameOptions`]" :key="index"
              :label="item.sApplyDrName" :value="item.sApplyDrName">
            </el-option>
          </el-select>
        </template>
        <template v-slot:sInpatientAreaText>
          <el-select v-model="condition.sInpatientAreaText" placeholder="" clearable filterable allow-create
            default-first-option style="width:100%" @change="mxDoSearch">
            <el-option v-for="(item, index) in optionsLoc[`sInpatientAreaTextOptions`]" :key="index" :label="item.sName"
              :value="item.sName">
            </el-option>
          </el-select>
        </template>
        <template v-slot:tags>
          <el-select v-model="condition.tags" placeholder="" multiple collapse-tags clearable filterable
            default-first-option style="width:100%" @change="changesTags">
            <el-option v-for="item in optionsLoc.collectTagOptions" :key="item.sId" :label="item.sTagName"
              :value="item.sTagName">
            </el-option>
          </el-select>
        </template>
      </SearchList>
    </template>
    <template v-slot:action>
      <!-- 按钮栏 -->
      <div class="flex justify-between">
        <div class="flex">
          <el-dropdown @command="handleDropMenu" @visible-change="dropdownVisibleChange"
            placement="bottom">
            <el-button type="primary" plain>
              <template #icon>
                <Icon name="el-icon-setting"></Icon>
              </template>
              设置<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="$auth['report:report:pageConfig']" @click="ReportSetting_Dialog = true">
                  <div class="inline-flex w-24 items-center p-0.5 leading-5">
                    <i class="fa fa-page-setting pr-1" style="height: 20px;"></i>页面设置
                  </div>
                </el-dropdown-item>
                <el-dropdown-item v-if="$auth['report:report:printConfig']" command="OpenPrintSet">
                  <div class="inline-flex w-32 items-center p-0.5 leading-5">
                    <i class="fa fa-print-text pr-1" style="height: 20px;"></i>打印设置
                  </div>
                </el-dropdown-item>
                <el-dropdown-item v-if="$auth['report:report:favoriteTag']" command="CollectTag">
                  <div class="inline-flex w-24 items-center p-0.5 leading-5">
                    <i class="fa fa-collect-case pr-1" style="height: 20px;"></i>收藏标签
                  </div>
                </el-dropdown-item>
                <el-dropdown-item v-if="$auth['report:report:signConfig']" @click="ElecSignatureVisible = true">
                  <div class="inline-flex w-24 items-center p-0.5 leading-5">
                    <i class="fa fa-sign-on pr-1" style="height: 20px;"></i>签章设置
                  </div>
                </el-dropdown-item>
                <el-dropdown-item v-if="$auth['report:report:dicomNodeConfig']" @click="DicomSettingVisible = true">
                  <div class="inline-flex w-36 items-center p-0.5 leading-5">
                    <i class="fa fa-node-tree pr-1" style="height: 20px;"></i>节点设置
                  </div>
                </el-dropdown-item>
                <el-dropdown-item v-if="$auth['report:report:testValueMng']" @click="QualityTemplateSetVisible = true">
                  <div class="inline-flex w-28 items-center p-0.5 leading-5">
                    <i class="fa fa-note-skeleton pr-1" style="height: 20px;"></i>质控评级设置
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-dropdown @command="handleDropMenu" style="margin-left:10px;" placement="bottom">
            <el-button-icon-fa type="primary" plain icon='fa fa-address-book'>
              病例管理<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button-icon-fa>
            <template #dropdown>

              <el-dropdown-item v-if="$auth['report:report:secretMng']" command="secretPatients">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-secret-icon pr-1" style="height: 20px;"></i>保密病例
                </div>
              </el-dropdown-item>
              <el-dropdown-item v-if="$auth['report:report:setSecret']" command="setSecretPatient">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-secretIcon pr-1" style="height: 20px;"></i>
                  置为保密
                </div>
              </el-dropdown-item>
              <el-dropdown-item v-if="$auth['report:report:dicomMng']" command="ImgMng">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-zhongjian pr-1" style="height: 20px;"></i>图像管理
                </div>
              </el-dropdown-item>
              <el-dropdown-item v-if="$auth['report:report:deletePatient']" command="delPatient">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-delete-case pr-1" style="height: 20px;"></i>删除病例
                </div>
              </el-dropdown-item>
              <el-dropdown-item v-if="$auth['report:report:recoverPatient']" command="restorePatient">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-restore pr-1" style="height: 20px;"></i>还原病例
                </div>
              </el-dropdown-item>
              <el-dropdown-item v-if="$auth['report:report:sendConsultation']" command="sendConsultation">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-data-back pr-1" style="height: 20px;"></i>发送会诊
                </div>
              </el-dropdown-item>
              <el-dropdown-item v-if="$auth['report:report:cancelConsultation']" command="cancelConsultation">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-cancle pr-1" style="height: 20px;"></i>取消会诊
                </div>
              </el-dropdown-item>
              <el-dropdown-item v-if="$auth['report:report:allocateReport']" command="assignReport">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-distribution pr-1" style="height: 20px;"></i>
                  报告分配
                </div>
              </el-dropdown-item>
              <!-- <el-dropdown-item v-if="$auth['report:report:kip']" command="kipMng">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-KIP pr-1" style="height: 20px;"></i>
                  KIP 管理
                </div>
              </el-dropdown-item> -->
            </template>
          </el-dropdown>

          <el-dropdown @command="handleDropMenu" style="margin-left:10px;" placement="bottom">
            <el-button-icon-fa type="primary" plain icon='fa fa-th-list'>
              批量操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button-icon-fa>
            <template #dropdown>

              <el-dropdown-item>
                <!-- 批量打印 -->
                <ReportPrintBtn v-if="$auth['report:report:batchPrint']" :multipleSelection="multipleSelection"
                  :propParams="{ isBatch: true, idKey: 'sId', deviceTypeIdKey: 'sRoomId', isMarkPrint: true, iModuleId: iModuleId }"
                  :buttonMsg="{}">
                  <div class="inline-flex w-24 items-center p-0.5 leading-5">
                    <i class="fa fa-print-text pr-1" style="height: 20px;"></i>
                    批量打印
                  </div>
                </ReportPrintBtn>
              </el-dropdown-item>
              <el-dropdown-item v-if="$auth['report:report:batchAudit']" command="batchAuditing">

                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-report-audit pr-1" style="height: 20px;"></i>
                  批量审核
                </div>

              </el-dropdown-item>
              <el-dropdown-item v-if="$auth['report:report:batchSendMessage']" command="BPMMsg">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-data-back pr-1" style="height: 20px;"></i>
                  批量回传
                </div>
              </el-dropdown-item>
              <el-dropdown-item v-if="$auth['report:report:batchExport']" @click="doExportTable(false)">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-file-excel-o pr-1" style="height: 20px;"></i>
                  批量导出
                </div>
              </el-dropdown-item>
              <el-dropdown-item v-if="$auth['report:report:batchExport']" @click="doExportTable(true)">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-file-excel-o pr-1" style="height: 20px;"></i>
                  匿名导出
                </div>
              </el-dropdown-item>
              <el-dropdown-item v-if="$auth['report:report:batchExport']" @click="mxExportSelectionTable">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-file-excel-o pr-1" style="height: 20px;"></i>
                  勾选导出
                </div>
              </el-dropdown-item>
              <el-dropdown-item v-if="$auth['report:report:batchPublish']" @click="onBatchRelease">
                <div class="inline-flex w-24 items-center p-0.5 leading-5">
                  <i class="fa fa-send-o pr-1" style="height: 20px;"></i>
                  批量发布
                </div>
              </el-dropdown-item>
              <!-- 发送图像 -->
              <SendImage v-if="$auth['report:report:batchSendDicom']" :multipleSelection="multipleSelection"></SendImage>
              
            </template>
          </el-dropdown>
        </div>
        <div class="flex items-center">
          <!-- <span class=" mx-2" v-if="unAuditCount != 0">
            <el-checkbox v-model="iIsUnaudited" :disabled="unAuditCount == 0" @change="mxDoSearch">未审核（{{ unAuditCount
            }}）</el-checkbox>
          </span> -->

          <span class=" mx-2">
            <el-checkbox v-model="isPreviewModel" :disabled="!tableData.length">预览模式</el-checkbox>
          </span>
          <!-- <span class=" mx-2">
                                <el-checkbox v-model="isCaseType"
                                    :disabled="iIsUnaudited"
                                    @change="checkCaseTypeChange">病例分类</el-checkbox>
                            </span> -->
          <span class="mx-2">
            <el-checkbox v-model="isCollect" :disabled="iIsUnaudited" @change="mxDoSearch">我的收藏</el-checkbox>
          </span>
          <!-- <span class=" mx-2">
            <el-checkbox v-model="isAppointDate" :disabled="iIsUnaudited" @change="mxDoSearch">预约日期</el-checkbox>
          </span> -->
          <!-- <span class=" mx-2">
            <el-checkbox v-model="isInjectTime" :disabled="iIsUnaudited" @change="mxDoSearch">注射日期</el-checkbox>
          </span> -->
          <el-button class="ml-8" @click="handleResetCondition()">
            <template #icon>
              <Icon name="el-icon-refresh-left">
              </Icon>
            </template>
            重置</el-button>
          <el-button type="primary" @click="mxDoSearch" :loading="loading">
            <template #icon>
              <Icon name="el-icon-search" color="white"></Icon>
            </template>
            查询</el-button>
        </div>

      </div>
    </template>
    <template v-slot:content>
      <DragAdjust class="flex w-full h-full" :dragAdjustData="computedDA1">
        <template v-slot:c1>
          <el-table-extend v-if="elementConfigData.reportTable" :iModuleId="iModuleId" storageKey="ReportCaseIndexTable"
            v-loading="loading" :data="tableData" ref="mainTable" :row-class-name="mxRowClassName"
            @row-dblclick="(row) => { $auth['report:report:reportInline'] && openProcess('Report', row) }" stripe @row-click="handleRowClick"
            @sort-change="mxOnSort" @selection-change="handleSelectionChange" highlight-current-row height="100%"
            style="width: 100%;" class="reportTable">
            <el-table-column fixed type="selection" label="选择" prop="_select" align="center" width="50">
            </el-table-column>
            <el-table-column label=" " prop="_index2" isController="true" align="center" width="1"
              :class-name="'tableColOverflowVisible'">
              <template v-slot="scope">
                <template v-if="scope.row.index === editLayer.selectedItem.index">
                  <div class="row-drawer" :style="styleVar">
                    <div class="flex-box">
                      <!-- <div v-if="scope.row.index === editLayer.selectedItem.index" class="avatar-box" >
                            <TableAvatar :patientInfo="scope.row" :index="scope.row.index"></TableAvatar>
                        </div> -->
                      <div class="drawer-right">
                        <div class="g-content ">
                          <div class="c-collect"></div>

                          <el-button v-if="$auth['report:report:reportInline']" oldicon="fa fa-edit" type="primary"
                            plain @click="openProcess('Report', scope.row)">报告</el-button>

                          <el-button v-if="$auth['report:report:webViewInline']"
                          oldicon="fa fa-reading-image-computer-browse" type="primary" plain
                            @click="openWebReadViewer(scope.row)">web阅图</el-button>

                          <el-button v-if="$auth['report:report:webRebuildInline']" oldicon="fa fa-zhongjian"
                            type="primary" plain @click="openWebReBuildViewer(scope.row)">web重建</el-button>

                          <!-- 报告预览 -->
                          <ReportPreviewBtn v-if="$auth['report:report:browseReportInline']"
                            :propParams="{ patient: scope.row, idKey: 'sId', deviceTypeIdKey: 'sRoomId', iModuleId: iModuleId }"
                            :buttonMsg="{ plain: true, type: 'primary' }">
                            <el-button oldicon="fa fa-article" type="primary" plain
                              style="margin-left: 9px;">预览报告</el-button>
                          </ReportPreviewBtn>

                          <!-- 打印 -->
                          <!-- && editLayer.selectedItem.isShow -->
                          <ReportPrintBtn v-if="$auth['report:report:printReportInline']"
                            :propParams="{ patient: scope.row, isBatch: false, idKey: 'sId', deviceTypeIdKey: 'sRoomId', iModuleId: iModuleId }">
                            <el-button oldicon="fa fa-print-line2" type="primary" plain
                              style="margin-left: 9px;">打印报告</el-button>
                          </ReportPrintBtn>

                          <!-- 刻录 -->
                          <CdburnBtn v-if="$auth['report:report:cdburnInline']"
                            :buttonMsg="{ name: '刻录', plain: true, type: 'primary' }"
                            :patientInfo="editLayer.selectedItem">
                            <el-button oldicon="fa fa-cd-fill" type="primary" plain
                              style="margin-left: 9px;">刻录</el-button>
                          </CdburnBtn>

                          <!-- 导出 -->
                          <ExportBtn v-if="$auth['report:report:exportImageInline']"
                            :buttonMsg="{ name: '导出图像', plain: true, type: 'primary' }"
                            :patientInfo="editLayer.selectedItem">
                            <el-button oldicon="fa fa-stock-push" type="primary" plain
                              style="margin-left: 9px;">导出图像</el-button>
                          </ExportBtn>

                          <ApplyListInfo v-if="$auth['report:report:applyOrderInline']" 
                            :isReportModule="true" :buttonMsg="{ name: '电子申请单', plain: true, type: 'primary'}" 
                            :patientInfo="editLayer.selectedItem" type="primary" plain></ApplyListInfo>

                          <!-- <el-button v-if="$auth['report:report:FlowRecord']" type="primary" plain @click="onFlowRecordClick"
                              style="margin-left: 10px">流程记录</el-button> -->

                            <el-button v-if="$auth['report:report:signVerify']" type="primary" plain @click="d_signatureV_v = true">签名信息</el-button>

                            <el-button v-if="$auth['report:report:batchSendDicom']" type="primary" plain @click="openSendImageDialog">发送图像</el-button>
                            
                        </div>
                        <div class="step-box w-full h-auto">
                          <FlowRecordSteps ref="FlowRecordSteps" :patientInfo="scope.row"></FlowRecordSteps>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </template>
            </el-table-column>
            <el-table-column type="index" label="序号" prop="_index" align="center" width="60">
            </el-table-column>
            <el-table-column v-for="item in elementConfigData.reportTable"
              :show-overflow-tooltip="item.sProp !== 'img'" :key="item.index" :prop="item.sProp" :label="item.sLabel"
              :fixed="item.sFixed" :align="item.sAlign" :width="item.sWidth" :min-width="item.sMinWidth"
              :sortable="(!!item.iSort) ? 'custom' : false" :isSortable="item.isSortable" :sOrder="item.sOrder" 
              :column-key="item.sSortField ? item.sSortField : null" :iIsHide="item.iIsHide">
              <template v-slot="scope">
                <!-- <template v-if="item.sProp === 'iPriority'">
                    <PriorityMark v-if="scope.row.iPriority || scope.row.index === editLayer.selectedItem.index" 
                        :patient="scope.row" :currentIndex="editLayer.selectedItem.index"></PriorityMark>
                </template> -->
                <template v-if="item.sProp === 'tag'">
                  <div v-if="scope.row.specialFlag" class="inline-block" title="">
                    <i style="color:#ba3e3e; font-size:20px;margin-right:4px" class="fa fa-secret-icon"></i>
                  </div>
                  <div v-if="scope.row.iCollect || scope.row.index === editLayer.selectedItem.index" class="c-star" title="已收藏">
                    <EnshrinePatient :patient="scope.row"
                      @callBack="(val) => { scope.row.iCollect = val.iCollect; scope.row.sCollectionTag = val.sCollectionTag; scope.row.sCollectionTagId = val.sCollectionTagId; scope.row.sCollectionReason = val.sCollectionReason }">
                    </EnshrinePatient>
                  </div>

                </template>
                <template v-else-if="FlowStateEnum.includes(item.sProp)">
                  <template v-if="item.sProp === 'iIsReport'">
                    <!-- 报告 -->
                    <i v-if="scope.row.iReportStatus === 11" class="fa fa-pencil-mark blue"></i>
                    <i v-else-if="scope.row.iReportStatus === 12" class="fa fa-tick-line green"></i>
                  </template>
                  <template v-else-if="item.sProp === 'iIsApprove'">
                    <!-- 审核 -->
                    <i v-if="scope.row.iExamineStatus === 21" class="fa fa-pencil-mark blue"></i>
                    <i v-else-if="scope.row[`${item.sProp}`]" class="green"
                      :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                  </template>
                  <template v-else-if="item.sProp === 'iIsFinalApprove'">
                    <!-- 复审 -->
                    <i v-if="scope.row.iFinalStatus === 31" class="fa fa-pencil-mark blue"></i>
                    <i class="green" v-else-if="scope.row[`${item.sProp}`]"
                      :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                  </template>

                  <template v-else-if="item.sProp === 'iIsRegister'">
                    <!-- 签到 -->
                    <i v-if="scope.row[`${item.sProp}`]" class="icon-green"
                      :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                  </template>
                  <template v-else-if="item.sProp === 'iIsPrintText'">
                    <!-- 打印 -->
                    <i v-if="scope.row[`${item.sProp}`] === -1" class="red" :class="`fa fa-times-circle-o`"></i>
                    <i v-else-if="scope.row[`${item.sProp}`]" class="green"
                      :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                  </template>
                  <template v-else>
                    <i v-if="scope.row[`${item.sProp}`]" class="green" :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                  </template>
                </template>
                <template v-else-if="item.sProp === 'iConsultation'">
                  <span :class="scope.row[`${item.sProp}`] == 1 ? 'green' : ''">{{ scope.row[`${item.sProp}`] ==
                    1 ? '已发送' : '未发送' }}</span>
                </template>
                <template v-else-if="item.sProp === 'sLockUserName'">
                  <div class="inline-flex items-center">
                    <svg v-if="scope.row[`${item.sProp}`]" class="fa" aria-hidden="true"
                      style="position:relative;top:-2px;margin-right:5px">
                      <use xlink:href="#fa-lock1"></use>
                    </svg>
                    <span>{{ scope.row[`${item.sProp}`] }}</span>
                  </div>
                </template>
                <template v-else-if="['sNuclideSupName', 'sTracerSupName'].includes(item.sProp)">
                    <span v-if="scope.row[item.sProp]" v-html="scope.row[item.sProp]"></span>
                </template>
                <template v-else-if="['fRecipeDose'].includes(item.sProp)">
                    {{ setRecipeDose(scope.row[item.sProp], scope.row.sRecipeDoseUnit) }}
                </template>
                <template v-else-if="['fBloodSugar'].includes(item.sProp)">
                    {{ setBloodSugar(scope.row[item.sProp]) }}
                </template>
                <template v-else-if="['iIsPregnant'].includes(item.sProp)">
                    {{ setPregnantText(scope.row[item.sProp]) }}
                </template>
                <template v-else-if="item.sProp.slice(0, 1) === 'd'">
                  {{ mxFormatterDate(scope.row[`${item.sProp}`]) }}
                </template>
                <template v-else>
                  {{ scope.row[`${item.sProp}`] }}
                </template>
              </template>
            </el-table-column>
          </el-table-extend>
        </template>
        <template v-slot:c2>
          <Preview v-model:patient="editLayer.selectedItem"></Preview>
        </template>
      </DragAdjust>
    </template>
    <template #footer>
      <!-- 分页 -->
      <el-pagination background @size-change="onSizeChange" @current-change="onCurrentChange"
        :current-page="page.pageCurrent" :page-sizes="mxPageSizes" :pager-count="5" :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next" :total="page.total">
      </el-pagination>
    </template>
  </LayoutTable>

  <!-- 采集头像 -->
  <!-- <CollectAvatar :dialogVisible="d_CollectAvatar_v" :patientInfo="editLayer.selectedItem"
    :index="editLayer.selectedItem.index" @refreshImg="refreshImg" @closeDialog="closeCollectAvatarDialog">
  </CollectAvatar> -->

  <!-- 打印模板 -->
  <!-- <PrintTemplate :dialogVisible="d_PrintTemplate_v" @closeDialog="closePrintTemplateDialog"></PrintTemplate> -->

  <!-- 图像管理 -->
  <ImageCase v-model:dialogVisible="d_ImageCase_v"></ImageCase>

  <!-- 收藏标签管理 -->
  <CollectTag v-model:dialogVisible="d_CollectTag_v"></CollectTag>

  <!-- 打印设置 -->
  <PrintSet :dialogVisible="d_printSet_v" :iModuleId="iModuleId" @closeDialog="closePrintSetDialog"></PrintSet>

  <DicomSetting v-model="DicomSettingVisible"></DicomSetting>

  <ElecSignature v-model="ElecSignatureVisible"></ElecSignature>

  <QualityTemplateSet v-model="QualityTemplateSetVisible"></QualityTemplateSet>

  <!-- 保密病例 -->
  <SecretPatients v-model:dialogVisible="secret_Patient_Dialog" @onCloseDialog="closeSecretPatientDialog"></SecretPatients>

  <!-- 分配报告 -->
  <AssignReport :multipleSelection="multipleSelection" v-model="assignReport_Dialog" @reFreshTable="mxDoRefresh">
  </AssignReport>

  <!-- KIP管理 -->
  <!-- <KIPMng  v-model="kipMng_Dialog"></KIPMng> -->


  <!--滑层， 报告-->
  <TransitionSlide :currentProcess="!!currentProcess">
    <Report v-if="!!currentProcess" ref="reportEditPageCompRef" :currentProcess="currentProcess"
      @closeClick="closePenalProcess">
      <template #patientList>
        <!-- 报告页内的患者列表 -->
        <LayoutTable>
          <template #header>
            <div class="flex flex-col">
              <div class="flex">
                <SearchList v-model:modelValue="condition" v-model:list="innerSearchListConfig" :optionData="optionsLoc"
                  :loading="loadingSearch" :iModuleId="iModuleId" storageKey="ReportCaseIndexInnerSearch" labelWidth="90px"
                  :minRowsNum="2" @clickSearch="mxDoSearch" @changeSearch="mxDoSearch" @reset="handleResetCondition()">
                  <template v-slot:dAppointmentTimeSt>
                    <el-date-picker v-model="condition.dAppointmentTimeSt" type="date"
                      :picker-options="pickerOptionsAppointDayStart" @change="changeAppointTime">
                    </el-date-picker>
                  </template>
                  <template v-slot:dAppointmentTimeEd>
                    <el-date-picker v-model="condition.dAppointmentTimeEd" type="date" @change="changeAppointTime">
                    </el-date-picker>
                  </template>
                  <template v-slot:sRecentDays>
                    <el-select v-model="condition.sRecentDays" @change="changeTimes" placeholder="" clearable>
                      <el-option v-for="(item, index) in optionsLoc.recentDayOptions" :key="index" :label="item.sName"
                        :value="item.keyWord">
                      </el-option>
                    </el-select>
                  </template>
                </SearchList>
              </div>
              <div class="absolute right-3.5 top-12.5">
                <el-button type="primary" plain @click="mxDoSearch" :loading="loading"> 
                  查询
                </el-button>
              </div>
            </div>
          </template>

          <template #content>
            <el-table-extend v-if="elementConfigData.reportTable" :iModuleId="iModuleId"
              storageKey="ReportCaseIndexInnerTable" v-loading="loading" :data="tableData"
              :row-class-name="getCustomTableRowClass" @row-dblclick="(row) => dblclickInnerTableRow(row)" 
              stripe height="100%" style="width: 100%;">
              <el-table-column v-for="item in innerTableColumnList" :show-overflow-tooltip="item.sProp !== 'img'"
                :key="item.index" :prop="item.sProp" :label="item.sLabel" :fixed="item.sFixed" :align="item.sAlign"
                :width="item.sWidth" :min-width="item.sMinWidth" :sortable="(!!item.iSort) ? true : false"
                :column-key="item.sSortField ? item.sSortField : null">
                <template v-slot="scope">
                  <template v-if="FlowStateEnum.includes(item.sProp)">
                    {{ !!scope.row[`${item.sProp}`] ? '√' : '' }}
                  </template>
                  <template v-else-if="item.sProp === 'iConsultation'">
                    <span :class="scope.row[`${item.sProp}`] == 1 ? 'green' : ''">
                      {{ scope.row[`${item.sProp}`] == 1 ? '已发送' : '未发送' }}</span>
                  </template>
                  <template v-else>
                    {{ scope.row[`${item.sProp}`] }}
                  </template>
                </template>
              </el-table-column>
            </el-table-extend>
          </template>
          <template #footer>
            <div class="flex justify-end items-center">
              <el-pagination background @size-change="onSizeChange" @current-change="onCurrentChange"
                :current-page="page.pageCurrent" :page-sizes="mxPageSizes" :pager-count="5" :page-size="page.pageSize"
                layout="total, sizes, prev, next" :total="page.total">
              </el-pagination>
            </div>
          </template>
        </LayoutTable>

      </template>
    </Report>
  </TransitionSlide>


  <!-- 还原病例 -->
  <RestorePatient v-model:dialogVisible="restore_Patient_Dialog"></RestorePatient>

  <!-- 病例类型管理 -->
  <!-- <CaseType :dialogVisible="caseType_Dialog" @onCloseDialog="onCloseCaseType"></CaseType> -->

  <ReportSetting v-model="ReportSetting_Dialog"></ReportSetting>

  <SignatureVerification v-model:dialogVisible="d_signatureV_v" :patientInfo="editLayer.selectedItem" :sReportId="editLayer.selectedItem.reportId" readonly></SignatureVerification>

  <!-- 发送图像弹窗 -->
  <SendImageDialog v-model:dialogVisible="sendImageDialogVisible" :moduleName="'report_module'" :patientInfo="editLayer.selectedItem" ></SendImageDialog>

  <el-dialog v-if="processDialog" append-to-body title="审核进度" :modelValue="processDialog" width="700px" top="30vh"
    :before-close="handleCloseProcess">
    <div class="batch-audit">
      <p class="audit-status"><span :style="{ color: successNum == canAuditArr.length ? '#2cc964' : '#308fe8' }">
          {{ auditStatus }}</span>第{{ nowToAudit }}条 | 共{{ canAuditArr.length }}条
      </p>
      <el-progress :stroke-width="14" :color="percentageNum == 100 ? '#2cc964' : ''"
        :percentage="percentageNum"></el-progress>
      <p class="audit-selected">
        您已选择{{ multipleSelection.length }}条报告记录，可提交记录{{ canAuditArr.length }}条。提交失败{{ failToAudit.length }}条。
        <span v-if="failToAudit.length">失败记录：<span v-for="(item, index) in failToAudit" :key="index" style="color:red">{{
          item.sName }}&nbsp;&nbsp;</span></span>
      </p>
    </div>
  </el-dialog>
  <el-dialog append-to-body title="消息类型" :modelValue="d_sendMsg_v" :close-on-click-modal="false" width="500px" top="15vh"
    class="t-default my-dialog" @close="d_sendMsg_v = false">
    <div class="c-body" style="padding-left: 190px;min-height: 200px;">
      <el-checkbox-group v-model="formMessage" class="c-item-list" style="overflow: hidden;">
        <div v-for="item in optionsLoc.BPMMsg" :key="item.sValue">
          <el-checkbox :label="item.sValue"
            style="margin-bottom:10px;">{{ item.sName }}
          </el-checkbox>
        </div>
      </el-checkbox-group>
    </div>
    <template #footer>
        <el-button-icon-fa type="primary" @click="onSendBPMMsgClick" icon="fa fa-send">发送</el-button-icon-fa>
        <el-button-icon-fa @click="d_sendMsg_v=false" icon="fa fa-close-1">关闭</el-button-icon-fa>
    </template>
  </el-dialog>
</template>
<script>
//模块辅助样式

// 混入
import {
  getStoreNameByRoute, mixinElementConfigs, mixinTable, mixinAvatar, mixinTableInner,
  openWebReadImgOrRebuild, closeWindow, mixinExportExcel
} from '$supersetResource/js/projects/apricot/index.js'

// 配置
import Configs from './configs'
import CaseReportConfigs from '$supersetViews/apricot/case/report/configs/index.js'
import { reportEnum, caseEnum } from '$supersetResource/js/projects/apricot/enum.js'
import { deepClone } from "$supersetUtils/function"
import { recentDayOptions, appointmentEnum } from '$supersetResource/js/projects/apricot/enum.js'
import { transformDate } from '$supersetResource/js/tools'
// import { getToken } from '$supersetUtils/auth'

import { caseTypeTree } from '$supersetApi/projects/apricot/system/caseType.js'
import { getCollectionTagData } from '$supersetApi/projects/apricot/common/collectTag.js'
import {
  getItemPositionData, getNuclideData, getTracerData,
} from '$supersetApi/projects/apricot/appointment/projectSet.js'
import {
  queryClientConfig, readClientId, verificationCode,
  getComboInPatient
} from '$supersetApi/projects/apricot/case/report.js' // 获取报告医生 获取审核医生
import { getApplyDept, getApplyDr } from '$supersetApi/projects/apricot/retrieve'
import Api from '$supersetApi/projects/apricot/appointment/patientInfo.js'
import { getReportAboveDrData } from '$supersetResource/js/projects/apricot/useHandlerSelect.js' // 获取报告医生
import { BPMMsgCreate } from '$supersetApi/projects/apricot/case/report.js'
import { createNamespacedHelpers } from 'vuex';
import { defineAsyncComponent } from 'vue';

// 引入获取院区，机房，项目方法
import { useGetHospitalData, useGetMachineRoomData, useGetItemData, useChangeHospital, useChangeMachineRoom } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'

const { mapMutations } = createNamespacedHelpers('apricot/report_module')


export default {
  name: 'apricot_Report',
  mixins: [mixinElementConfigs, mixinTable, mixinAvatar, mixinTableInner, mixinExportExcel],
  components: {
    RestorePatient: defineAsyncComponent(()=> import('$supersetViews/apricot/reportCase/components/restorePatient.vue')),
    // caseType: defineAsyncComponent(()=> import('$supersetViews/apricot/reportCase/components/caseType.vue')),
    AssignReport: defineAsyncComponent(()=> import('$supersetViews/apricot/reportCase/components/assignReport.vue')),
    // KIPMng: defineAsyncComponent(()=> import('$supersetViews/apricot/reportCase/components/KIPMng/Index.vue')),
    ReportSetting: defineAsyncComponent(()=> import('$supersetViews/apricot/reportCase/components/ReportSetting.vue')),
    // TableAvatar: defineAsyncComponent(() => import('$supersetViews/apricot/components/TableAvatar.vue')),
    LockPatient: defineAsyncComponent(() => import('$supersetViews/apricot/components/LockPatient.vue')),
    EnshrinePatient: defineAsyncComponent(() => import('$supersetViews/apricot/components/EnshrinePatient.vue')),
    FlowRecordSteps: defineAsyncComponent(() => import('$supersetViews/apricot/components/FlowRecordSteps.vue')),

    ReportPreviewBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/ReportPreviewBtn.vue')),
    ReportPrintBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/ReportPrintBtn.vue')),
    CdburnBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/CdburnBtn.vue')),
    ExportBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/ExportBtn.vue')),
    // PrintTemplate: defineAsyncComponent(() => import('$supersetViews/apricot/components/PrintTemplate.vue')),
    // CollectAvatar: defineAsyncComponent(() => import('$supersetViews/apricot/components/CollectAvatar.vue')),
    ImageCase: defineAsyncComponent(() => import('$supersetViews/apricot/components/ImageCase.vue')),
    CollectTag: defineAsyncComponent(() => import('$supersetViews/apricot/components/CollectTag.vue')),
    Report: defineAsyncComponent(() => import('$supersetViews/apricot/case/report/Index.vue')),
    Preview: defineAsyncComponent(() => import('$supersetViews/apricot/reportCase/components/Preview.vue')),
    PrintSet: defineAsyncComponent(() => import('$supersetViews/apricot/components/PrintSet.vue')),
    DicomSetting: defineAsyncComponent(() => import('$supersetViews/apricot/reportCase/components/DicomSetting.vue')),
    ElecSignature: defineAsyncComponent(() => import('$supersetViews/apricot/reportCase/components/ElecSignature.vue')),
    SecretPatients: defineAsyncComponent(() => import('./components/secretPatients.vue')),
    SendImage: defineAsyncComponent(() => import('$supersetViews/apricot/reportCase/components/SendImage.vue')),
    SendImageDialog: defineAsyncComponent(() => import('$supersetViews/apricot/reportCase/components/SendImageDialog.vue')),
    ApplyListInfo: defineAsyncComponent(() => import('$supersetViews/apricot/components/ApplyListInfo.vue')),
    QualityTemplateSet: defineAsyncComponent(() => import('$supersetViews/apricot/reportCase/components/QualityTemplateSet.vue')),
    // PriorityMark: defineAsyncComponent(() => import('./components/PriorityMark.vue')),
    SignatureVerification: defineAsyncComponent(() => import('$supersetViews/apricot/components/SignatureVerification.vue')),
  },
  data() {

    return {
      iModuleId: 6, // 报告管理标识 ，eName: 'REPORT'， 在mixinPrintPreview混合模块中调用
      isMixinDynamicGetTableHead: true,   // 是否动态获取表头
      searchListConfig: [...Configs.searchStyle, ...Configs.searchForm],
      innerSearchListConfig: Configs.innerSearchItems,
      currentProcess: '',
      condition: {
        dAppointmentTimeSt: '',
        dAppointmentTimeEd: new Date(),
        iIsImaging: null,
        iIsPrint: null,
        iConsultation: null,
        iIsReportCommit: null,
        iIsApprove: null
      },
      isAppointDate: false,
      pickerOptionsAppointDayStart: {
        disabledDate: time => {
          // if (this.condition.dAppointmentTimeEd) {
          //   return time.getTime() > new Date(this.condition.dAppointmentTimeEd).getTime()
          // }
          // return time.getTime()
        }
      },
      pickerOptionsAppointDayEnd: {
        disabledDate: time => {
          // if (this.condition.dAppointmentTimeSt) {
          //   return time.getTime() < new Date(this.condition.dAppointmentTimeSt).getTime()
          // }
          // return time.getTime()
        }
      },
      isInjectTime: false,
      isCollect: false,
      isCaseType: false,
      optionsLoc: {
        // inject: reportEnum.inject,
        // machine: reportEnum.machine,
        qualitative: caseEnum.qualitative,
        iIsReportCommitOptions: reportEnum.report,
        iIsApproveOptions: reportEnum.approve,
        iConsultationOptions: reportEnum.consultation,
        iIsImagingOptions: reportEnum.iIsImaging,
        iIsPrintOptions: reportEnum.iIsPrint,
        recentDayOptions: recentDayOptions,
        positionOptions: [],
        nuclideOptions: [],
        tracerOptions: [],
        collectTagOptions: [],
        sReporterNameOptions: [],
        sApproveNameOptions: [],
        sPracticeNameOptions: [],
        sSourceOptions: appointmentEnum.visitTypeOptions,
        sFinalExamineNameOptions: [],
        sApplyDepartTextOptions: [],
        sApplyPersonNameOptions: [],
        sInpatientAreaTextOptions: [],
        BPMMsg: this.$store.getters['dict/map'].BPMMsg || [],
        districtArrOption: [], //院区
        machineRoomArrOption: [], // 机房
        itemsArrOption: [],    // 项目
        iIsFinalApproveOptions: reportEnum.finalApprove,
      },
      d_PrintTemplate_v: false,
      seriesList: [],
      multipleSelection: [],
      d_ImageCase_v: false,
      d_FlowRecord_v: false,
      d_CollectTag_v: false,
      treeLoading: false,
      treeData: [],
      defaultProps: {
        children: 'childs',
        label: 'sName'
      },
      DicomSettingVisible: false,
      QualityTemplateSetVisible: false,
      ElecSignatureVisible: false,
      innerTableColorConfigs: Configs.innerTableColorConfigs,
      isPreviewModel: window.localStorage.getItem('ReportCaseIndex-isShowPreviewModel') == 'true',
      processDialog: false, // 提交报告进度dialog
      percentageNum: 0.00, // 进度百分比
      successNum: 0, // 审核成功数量
      auditStatus: '', // 审核状态
      canAuditArr: [], // 可提交审核数组
      nowToAudit: 0, // 正在提交第几条记录
      failToAudit: [],
      unAuditCount: 0,
      iIsUnaudited: false,// 未审选择
      d_printSet_v: false,
      clientParams: {
        needVerifyLicence: null, //阅图是否需要授权
        verificationStatus: null, // 授权状态
        clientId: null,
      },
      isShowAvatar: false,
      d_sendMsg_v: false,
      formMessage: [],
      restore_Patient_Dialog: false, // 还原病例框
      caseType_Dialog: false, // 病例类型管理
      secret_Patient_Dialog: false,// 保密病例
      ReportSetting_Dialog: false, // 报告编辑页面设置
      assignReport_Dialog: false, // 分配报告弹窗 
      kipMng_Dialog: false, // 分配报告弹窗 
      page: {  // 分页	
        pageCurrent: 1,
        pageSize: localStorage.reportMngPageSize ? JSON.parse(localStorage.reportMngPageSize) : 50,    // 广东医科大学湛江附属医院需求分页默认查询50条
        total: 0
      },
      loadingSearch: false,
      d_signatureV_v: false,
      sendImageDialogVisible: false, // 发送图像弹窗
    }
  },
  computed: {
    workStation() {
      let temp = this.$store.getters['user/workStation'];
      return temp
    },
    userInfo() {
      let temp = this.$store.getters["user/userSystemInfo"];
      if (temp.__proto__.constructor === Object) {
        return temp;
      } else {
        return {};
      }
    },
    isFixedWostation() {
        return this.workStation.stationTypeCode === this.iModuleId.toString()
    },
    innerTableColumnList() {
      const propList = ['sName', 'sSexText', 'sAge', 'sLockUserName', 'iConsultation',
        'iIsRegister', 'iIsImaging',
        'iIsReport', 'iIsReportCommit',
        'iIsApprove',
        'iIsFinalApprove',
        'iIsPrintText',
        'iIsPrintImg',
        'sReporterName',
        'sExamineName',
        'sFinalExamineName',
        'sPracticeName',
        'sProjectName',
      ]
      return (this.elementConfigData.reportTable || []).filter(i => {
        return !i.iIsHide && propList.includes(i.sProp)
      })
    },

    patientInfo() {
      const module = getStoreNameByRoute(this.$route.name);
      let info = this.$store.getters['apricot/' + module + '/patientInfo'];
      return info || {};
    },

    computedDA1() {
      const panelConfig = [{
        size: 0,
        minSize: 100,
        name: "c1",
        isFlexible: true
      },
      {
        size: 750,
        minSize: 10,
        maxSize: 1700,
        name: "c2",
        isFlexible: false
      }
      ].filter(i => this.isPreviewModel ? 1 : i.name === 'c1')
      const DA1 = {
        type: 't-x',
        localStorageKey: '202308100002',
        panelConfig: panelConfig
      }
      return DA1
    },
    styleVar() {
      // console.log(this.$refs.mainTable?.tableRef);
        return {
          "--activeTableWidth": this.$refs.mainTable?.tableRef?.bodyWidth
        }
    },
  },
  watch: {
    'workStation': {
      async handler(val, oldVal) {
        if (val && oldVal) {
          // 赋值院区Id到查询条件
          if(val.districtId === this.condition.sDistrictId) {
            return
          }
          this.condition.sDistrictId = val.districtId;
          // 清空机房、项目查询条件
          this.condition.sMachineryRoomId = '';
          this.condition.sProjectId = '';
          // 获取患者表格数据
          oldVal && this.mxDoSearch();
          // 根据院区查询相应的机房数据
          await this.useGetMachineRoomData(val.districtId);
          if(this.optionsLoc.machineRoomArrOption.length) {
            // 院区匹配到机房，查询检查项目
            this.useGetItemData();
          } else {
            // 匹配不到机房，清空项目
            this.optionsLoc.itemsArrOption = [];
          }
          const target = {
            sDistrictId: this.condition.sDistrictId,
            sMachineryRoomId: ''
          }
          this.setReportCaseConditionCache(target);
        }
      },
      immediate: true
    },
    'page.pageSize'(val) {
      localStorage.setItem('reportMngPageSize', val);
    },
    isPreviewModel(val) {
      window.localStorage.setItem('ReportCaseIndex-isShowPreviewModel', val)
    }

  },
  created() {

  },
  methods: {
    ...mapMutations([
      'setPatientInfo',
      'setCurrentModule'
    ]),
    useGetHospitalData,
    useGetMachineRoomData,
    useGetItemData,
    useChangeHospital,
    useChangeMachineRoom,

    handleRowClick(row) {
      this.onClickRow(row);
    },
    // 处理数据并发起导出请求
    doExportTable(isAnonymous = false) {
      // 复制查询条件
      let condition = deepClone(this.condition);
      
      const dInjectTime = condition.dInjectTime
      if(dInjectTime) {
        condition.dInjectTimeSt = moment(condition.dInjectTime).startOf('day').toDate();
        condition.dInjectTimeEd = moment(condition.dInjectTime).endOf('day').toDate();
        delete condition.dInjectTime;
      }
      condition.iIsCancel = 0;

      // 实际用的查询字段为sProjectIds，sMachineryRoomIds 但因为很多地方用到sProjectId，sMachineryRoomId
      condition.sProjectIds = condition.sProjectId;
      delete condition.sProjectId;
      condition.sMachineryRoomIds = condition.sMachineryRoomId;
      delete condition.sMachineryRoomId;

      // 删除多余的字段
      delete condition.sRecentDays;

      const sModuleName = 'report';

      const filename = '报告管理';

      // 调用统一混入导出方法   
      this.mxDoExportTableByModule(condition, sModuleName, isAnonymous, filename);
    },
    setDefaultSearchTimes() {
      let today = new Date();
      let weekDay = today.getDay();
      if (weekDay == 1 || weekDay == 0) {
        this.condition.dAppointmentTimeSt = moment(today).add(-3, 'days').toDate();
      }
      else if (weekDay == 6) {
        this.condition.dAppointmentTimeSt = moment(today).add(-2, 'days').toDate();
      } else {
        this.condition.dAppointmentTimeSt = moment(today).add(-1, 'days').toDate();
      }
      this.condition.dAppointmentTimeEd = new Date(today.getTime())
    },
    // 下拉功能
    handleDropMenu(command) {
      // command指令对应的自定义方法  
      const oFunctionItem = {
        PrintTemplate: 'openPrintTemplate',
        ImgMng: 'openImageCase',
        CollectTag: 'onCollectTagClick',
        warningSetting: 'openWarningSetting',
        sendConsultation: 'onBatchSendConsultation',
        cancelConsultation: 'onBatchCancelConsultation',
        OpenPrintSet: 'onOpenPrintSet',
        BPMMsg: 'onBPMMsg',
        delPatient: 'onBatchDelPatient',
        restorePatient: 'onOpenRestorePatient',
        caseType: 'onOpenCaseType',
        batchAuditing: 'onBatchAuditing',
        setSecretPatient: 'setSecretPatient',
        secretPatients: 'onOpenSecretPatients',
        assignReport: 'openAssignReportDialog',
        kipMng: 'openKIPMngDialog'
      }
      // 执行相应指令的方法
      this[oFunctionItem[command]] && this[oFunctionItem[command]]();
    },
    // 打开KIP弹窗
    openKIPMngDialog() {
        this.kipMng_Dialog = true;
    },
    // 打开分配报告弹窗
    openAssignReportDialog() {
        if (!this.multipleSelection.length) {
            this.$message.warning('请选择需要分配的报告！')
            return
        }
        this.assignReport_Dialog = true;
    },
    // 置为保密病例
    setSecretPatient() {
      if (!this.multipleSelection.length) {
        this.$message.error('请选择需要保密的病例!')
        return
      }
      let userNames = ''
      let patientInfoIds = []
      this.multipleSelection.forEach((item, index) => {
        if (!item.specialFlag) {
          patientInfoIds.push(item.sId)
          userNames = item.sName + `${index == 0 ? '' : '、'}` + userNames
          return item
        }

      })
      this.$confirm(`确定要把【${userNames}】置为保密病例吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        Api.setSecretPatients(patientInfoIds).then(res => {
          if (res.success) {
            this.$message.success(res.msg)
            this.mxGetTableList()
            return
          }
          this.$message.error(res.msg)
        })
      })
    },
    // 打开保密病例
    onOpenSecretPatients() {
      this.secret_Patient_Dialog = true
    },
    // 关闭保密病例
    closeSecretPatientDialog(val) {
      if (val) {
        this.mxDoRefresh()
      }
      this.secret_Patient_Dialog = false
    },
    //  打开还原病历
    onOpenRestorePatient() {
      this.restore_Patient_Dialog = true
    },
    //  关闭还原病历
    closeResPatientDialog(val) {
      this.restore_Patient_Dialog = false
    },
    // 打开病例类型管理
    onOpenCaseType() {
      this.caseType_Dialog = true
    },
    // 关闭打开病例类型管理
    onCloseCaseType() {
      this.caseType_Dialog = false
    },

    dropdownVisibleChange(bool) {
    },
    // 
    // 展开查询条件页面
    switchRetrieveKey() {
      this.getOptionsData('positionOptions', getItemPositionData);
      this.getOptionsData('nuclideOptions', getNuclideData);
      this.getOptionsData('tracerOptions', getTracerData);
      // this.getOptionsData('testModeOptions', getTestModeData);
      this.getCollectionTagData();

      this.getApplyDept();
      this.getApplyDr();
      this.getComboInPatient()

    },
    // 请求接口获取下拉数据
    getOptionsData(optionName, callback) {
      if (this.optionsLoc[optionName].length) {
        return
      }
      let jsonData = {
      }
      callback(jsonData).then(res => {
        if (res.success) {
          this.optionsLoc[optionName] = res?.data || [];
        }
      }).catch(() => { })
    },
    // 获取收藏标签下拉框数据
    getCollectionTagData() {
      getCollectionTagData().then(res => {
        this.loading = false;
        if (res.success) {
          this.optionsLoc.collectTagOptions = res.data || []
          return
        }
      }).catch(err => {
        console.log(err);
      })
    },


    getApplyDept() {
      if (this.optionsLoc.sApplyDepartTextOptions.length) {
        return
      }
      getApplyDept().then(res => {
        if (res.success) {
          this.optionsLoc.sApplyDepartTextOptions = res.data || [];
          return
        }
        this.$message.error(res.msg);
      })
    },
    getApplyDr() {
      if (this.optionsLoc.sApplyPersonNameOptions.length) {
        return
      }
      getApplyDr().then(res => {
        if (res.success) {
          this.optionsLoc.sApplyPersonNameOptions = res.data || [];
          return
        }
        this.$message.error(res.msg);
      })
    },

    getComboInPatient() {
      if (this.optionsLoc.sInpatientAreaTextOptions.length) {
        return
      }
      getComboInPatient().then(res => {

        if (!res.success) {
          this.$message.error(res.msg);
          return
        }

        this.optionsLoc.sInpatientAreaTextOptions = res.data || [];
      })
    },
    handleSelect(key) {
      this.activeIndex = key // 组件名
    },
    // 创建病例按钮点击事件
    openImageCase() {
      this.d_ImageCase_v = true
    },
    //  打开打印设置
    onOpenPrintSet() {
        if(!this.isFixedWostation) {
            this.$message.warning('请切换到报告工作站！')
            return
        }
        this.d_printSet_v = true;
    },
    closePrintSetDialog() {
      this.d_printSet_v = false;
    },
    // 消息发送
    onBPMMsg() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择患者数据！');
        return
      }
      this.d_sendMsg_v = true;
    },
    async onSendBPMMsgClick() {
      if (!this.formMessage.length) {
        this.$message.warning('请至少选择一个消息');
        return
      }
      let sMsgPoint = this.formMessage.join(';');
      let loading = this.loadFindTip('发送中...');
      let isStopSend = false;
      let len = this.multipleSelection.length;
      for (let i = 0; i < len; i++) {
        if (isStopSend) {
          // 停止循环；
          loading.close();
          return
        }
        let jsonData = {
          sPatientId: this.multipleSelection[i].sId,
          sMsgPoint: sMsgPoint
        }
        await BPMMsgCreate(jsonData).then(res => {
          if (res.success) {
            this.$message.success(res.msg);
            return
          }
          this.$message({
            message: `患者：${this.multipleSelection[i].sName}；${res.msg}`,
            type: 'error',
            showClose: true,
            duration: 5000
          })
          isStopSend = true;
        }).catch(err => {
          console.log(err)
          isStopSend = true;
        })
      }
      loading.close();
      this.d_sendMsg_v = false;
    },
    handleSelectionChange(rows) {
      this.multipleSelection = rows;
    },
    handleCloseProcess() {
      this.processDialog = false
      if (this.successNum !== 0) {
        this.mxGetTableList()
      }
      this.successNum = 0
      this.nowToAudit = 0
      this.percentageNum = 0
      this.canAuditArr = []
      this.auditStatus = ''
      this.failToAudit = []
    },
    // 批量审核
    onBatchAuditing() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择报告记录！');
        return
      }
      this.canAuditArr = this.multipleSelection.filter(item => {
        if (item.iReportStatus === 12 && item.iExamineStatus !== 22) {
          return item
        }
      })
      if (!this.canAuditArr.length) {
        this.$message.warning('没有可提交的报告记录，请重新选择！');
        return
      }
      this.processDialog = true
      this.auditStatus = '正在提交记录：'
      let arrLen = this.canAuditArr.length
      this.canAuditArr.forEach((item, index) => {
        let params = {
          passFlag: 1,
          reportId: item.reportId
        }
        Api.setAuditReport(params).then(res => {
          this.nowToAudit++
          this.auditStatus = this.nowToAudit === arrLen ? '审核完成 ：' : '正在提交记录：'
          if (res.success) {
            this.successNum++
            let audit = this.successNum / arrLen * 100
            this.percentageNum = audit.toFixed(2)
          } else {
            this.failToAudit.push(item)
            this.$message.error(res.msg)
          }
        }).catch(err => {
          console.log(err)
        })

      })
    },
    // 批量发布
    onBatchRelease() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择患者数据');
        return
      }
      let dataJson = this.multipleSelection.map(item => {
        return {
          sInnerIndex: item.sInnerIndex,
          sPatientId: item.sId
        }
      })
      Api.reportMultiRelease(dataJson).then(res => {
        if (res.success) {
          this.$message.success(res.msg);
          return
        }
        this.$message.error(res.msg);
      })
    },
    // 批量发送会诊
    onBatchSendConsultation() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择报告记录！');
        return
      }
      let sendReportArr = this.multipleSelection.filter(item => {
        if (item.iConsultation === 0) {
          return item
        }
      })
      if (!sendReportArr.length) {
        this.$message.error('没有可发送的报告记录，请重新选择！');
        return
      }
      let jsonData = []
      sendReportArr.forEach(item => {
        jsonData.push({ sPatientId: item.sId })
      })
      Api.batchSendConsultation(jsonData).then(res => {
        if (res.success) {
          this.$message.success(res.msg)
          this.mxDoSearch()
          return
        }
        let errorsArr = res.data
        let errRowIndex = this.multipleSelection.findIndex(item => item.sId === errorsArr[0].sPatientId)
        this.$message.error(res.msg)
        if (errRowIndex > 0) {
          for (let i = 0; i < errRowIndex; i++) {
            if (this.multipleSelection[i].iConsultation != 1) {
              this.multipleSelection[i]['iConsultation'] = 1
            }
          }
          //this.mxDoSearch()
        }
      }).catch(err => {
        console.log(err)
      })
    },
    // 批量取消会诊
    onBatchCancelConsultation() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择报告记录！');
        return
      }
      let sendReportArr = this.multipleSelection.filter(item => {
        if (item.iConsultation === 1) {
          return item
        }
      })
      if (!sendReportArr.length) {
        this.$message.warning('没有可取消的报告记录，请重新选择！');
        return
      }
      let jsonData = []
      sendReportArr.forEach(item => {
        jsonData.push({ sPatientId: item.sId })
      })
      Api.batchCancelConsultation(jsonData).then(res => {
        if (res.success) {
          this.$message.success(res.msg)
          this.mxDoSearch()
          return
        }
        let errorsArr = res.data
        let errRowIndex = this.multipleSelection.findIndex(item => item.sId === errorsArr[0].sPatientId)
        this.$message.error(res.msg)
        if (errRowIndex > 0) {
          for (let i = 0; i < errRowIndex; i++) {
            if (this.multipleSelection[i].iConsultation == 1) {
              this.multipleSelection[i]['iConsultation'] = 0
            }
          }
        }
      }).catch(err => {
        console.log(err)
      })
    },
    // 打开流程记录
    onFlowRecordClick() {
      this.d_FlowRecord_v = true
    },
    // 打开收藏标签管理
    onCollectTagClick() {
      this.d_CollectTag_v = true
    },
    // 复选框病例分类change 事件
    checkCaseTypeChange(val) {
      if (val && !this.condition.sRecord) {
        this.$message.warning('请选择病例类型！')
        return
      }
      this.mxDoSearch()
    },
    // 下拉选中最近几天触发查询
    changeTimes(val) {
      if (!val) {
        localStorage.setItem('reportSearchKey', '')
        this.setDefaultSearchTimes()
        this.mxDoSearch()
        return
      }
      let target = this.optionsLoc.recentDayOptions.find(item => item.keyWord == val);
      if(target) {
        this.condition.dAppointmentTimeSt = moment().add(target.dates[0], 'days').toDate();
        this.condition.dAppointmentTimeEd = moment().add(target.dates[1], 'days').toDate();
      }
      localStorage.setItem('reportSearchKey', val);
      this.mxDoSearch()
    },
    // 修改院区查询条件
    onChangeHospital() {
        const target = {
            sDistrictId: this.condition.sDistrictId,
            sMachineryRoomId: '',
        }
        this.setReportCaseConditionCache(target);
        this.useChangeHospital();
    }, 
    // 修改机房查询条件
    onChangeMachineRoom() {
        const target = {
            sDistrictId: this.condition.sDistrictId,
            sMachineryRoomId: this.condition.sMachineryRoomId?.length ? this.condition.sMachineryRoomId.join(',') : '',
        }
        this.setReportCaseConditionCache(target);
        this.useChangeMachineRoom(target.sMachineryRoomId);
    },
    // 设置查询条件缓存
    setReportCaseConditionCache(data) {
        var reportCaseCondition = this.getReportCaseConditionCache();
        Object.assign(reportCaseCondition, data)
        localStorage.setItem('reportCaseCondition', JSON.stringify(data));
    },
    // 获取缓存的查询条件
    getReportCaseConditionCache() {
        let reportCaseCondition = localStorage.getItem('reportCaseCondition') || '{}';
        reportCaseCondition = JSON.parse(reportCaseCondition);
        const sMachineryRoomId = reportCaseCondition.sMachineryRoomId
        reportCaseCondition.sMachineryRoomId = sMachineryRoomId ? sMachineryRoomId.split(',') : [];
        return reportCaseCondition;
    },
    // 选择注射日期触发查询
    changeInjectTime(val) {
      if (val) {
        this.isInjectTime = true
        this.condition.dInjectTime = val

      } else (
        this.isInjectTime = false
      )
      this.mxDoSearch()
    },
    // 改变收藏标签
    changesTags(val) {
      this.condition.tags = val
      this.mxDoSearch()
    },
    loadFindTip(text) {
      return this.$loading({
        lock: true,
        text: text || '文件生成中...',
        // 
        background: 'rgba(255, 255, 255, 0.5)',
        customClass: 'my-loading'
      });
    },
    // 打开web重建 
    async openWebReBuildViewer(row, iIsRebuild = 1) {
      let loading = this.loadFindTip();
      await this.getClientConfig()
      const needVerifyLicence = this.clientParams.needVerifyLicence
      // 若接口链接超时  默认值为null,
      if (needVerifyLicence == null) {
        loading.close()
        return
      }
      if (needVerifyLicence) {
        await this.getClientId()
        const clientId = this.clientParams.clientId
        if (!clientId) {
          loading.close()
          return
        }
        await this.getVerificationCode()
        if (!this.clientParams.verificationStatus) {
          loading.close()
          return
        }
      }
      loading.close();
      let info = {
        sPatientId: row.sId,
        deviceTypeId: row.sRoomId
      }
      openWebReadImgOrRebuild(iIsRebuild, this.userInfo.sId, info);
    },
    // 打开web阅图
    openWebReadViewer(row) {
      this.openWebReBuildViewer(row, 0)
    },
    // 打开发送图像弹窗
    openSendImageDialog() {
      this.sendImageDialogVisible = true;
    },
    // 查询客户端配置
    async getClientConfig() {
      await queryClientConfig().then(res => {
        if (res.success) {
          this.clientParams.needVerifyLicence = res.data.needVerifyLicence;
          return
        }
        this.$message.error(res.msg)
      }).catch(err => {
        console.log(err)
      })
    },
    // 查询本机主板序列号
    async getClientId() {
      await readClientId().then(res => {
        if (res.success) {
          this.clientParams.clientId = res.data;
          return
        }
        this.$message.error(res.msg)
      }).catch(err => {
        console.log(err)
      })
    },
    // 获取授权状态
    async getVerificationCode() {
      let params = {
        clientId: this.clientParams.clientId
      }
      await verificationCode(params).then(res => {
        if (res.success) {
          this.clientParams.verificationStatus = res.success;
          return
        }
        this.$message.error(res.msg)
      }).catch(err => {
        console.log(err)
      })
    },
    changeAppointTime() {
      // if (!this.isAppointDate) {
      //   this.isAppointDate = true
      // }
      this.mxDoSearch()
    },
    // 查询数据
    getData(obj) {
      let params = deepClone(obj);
      let dAppointmentTimeSt = params.condition.dAppointmentTimeSt;
      params.condition.dAppointmentTimeSt = this.mxFormateOneDayStart(dAppointmentTimeSt);

      // 当选择了结束时间，转换成23：59：59
      let dAppointmentTimeEd = params.condition.dAppointmentTimeEd;
      params.condition.dAppointmentTimeEd = this.mxFormateOneDayEnd(dAppointmentTimeEd);
      if (this.iIsUnaudited) {
        // this.isAppointDate = false
        this.isInjectTime = false
        this.isCollect = false
        this.isCaseType = false
        params.condition.iIsUnaudited = 1
      }
      delete params.condition.sRecentDays;
      let dInjectTime = params.condition.dInjectTime;
      if (dInjectTime) {
        params.condition.dInjectTimeSt = moment(dInjectTime).startOf('day').toDate();
        params.condition.dInjectTimeEd = moment(dInjectTime).endOf('day').toDate();
        delete params.condition.dInjectTime;
      }
      if (!this.isInjectTime) {
        delete params.condition.dInjectTime;
        delete params.condition.dInjectTimeSt;
        delete params.condition.dInjectTimeEd
      }

      //   实际用的查询字段为sProjectIds，sMachineryRoomIds 但因为很多地方用到sProjectId，sMachineryRoomId
      params.condition.sProjectIds = params.condition.sProjectId;
      delete params.condition.sProjectId

      params.condition.sMachineryRoomIds = params.condition.sMachineryRoomId;
      delete params.condition.sMachineryRoomId

      this.isCollect ? (params.condition.iCollect = 1) : (delete params.condition.iCollect);
      if (!this.isCaseType) {
        delete params.condition.sRecord
      }
      if (!params.condition.tags || !params.condition.tags.length) {
        delete params.condition.tags
      }
      params.condition.iIsCancel = 0;
      this.multipleSelection = [];
      Object.keys(params.condition).map(item => {
        if(params.condition[item] === '') {
            delete params.condition[item]
        }
      })
      Api.getReportMngData(params).then((res) => {
        this.tableData = [];
        if (res.success) {
          this.tableData = res.data.recordList == null ? [] : res.data.recordList
          this.page.total = res.data.countRow;
          this.loading = false;
          // 赋选中状态
          this.mxSetSelected()
          return;
        }
        this.loading = false;
        this.$message.error(res.msg)
      }).catch(() => {
        this.loading = false;
      })
    },
    updateTableInfo (id = null, idx = null) {
        const sId = id !== null ? id : this.editLayer.selectedItem.sId
        const index = idx !== null ? idx : this.editLayer.selectedItem.index
        Api.getReportMngRow({ sId }).then(res => {
            if (res.success) {
                let data = res.data;
                data.index = index;
                this.tableData.splice(index, 1, data);
                if(sId !== this.editLayer.selectedItem.sId) return
                this.editLayer.selectedItem = this.tableData[index]; // 更新选中值
                this.$refs.mainTable && this.$refs.mainTable.setCurrentRow(this.tableData[index]) // 设置选中值
                return;
            }
        })
    },
    openProcess(componentName, patientInfo) {
      this.initElementConfigData();
      // 把选中的患者存入到 store 中 patientInfo 对象中
      this.setPatientInfo({
        patientInfo
      })
      this.setCurrentModule({ name: componentName });
      this.currentProcess = componentName;
      this.isPreviewModel = false;

      let oAutoSaveTime = window.localStorage.getItem('oAutoSaveTime');
      if (oAutoSaveTime) {
        oAutoSaveTime = JSON.parse(oAutoSaveTime);
        if (oAutoSaveTime[patientInfo.sRoomId] == 2) {
          // 阅图
          patientInfo.iIsImaging && this.openWebReadViewer(patientInfo)
        } else if (oAutoSaveTime[patientInfo.sRoomId] == 1) {
          // 重建
          patientInfo.iIsImaging && this.openWebReBuildViewer(patientInfo);
        }
      } else {
        patientInfo.iIsImaging && this.openWebReadViewer(patientInfo)
      }
    },
    // 关闭报告编辑面板
    closePenalProcess() {
      this.currentProcess = '';
      this.updateTableInfo();
      this.toCloseWindow();
      this.$refs.FlowRecordSteps && this.$refs.FlowRecordSteps.flowRecordInfo();
    },
    toCloseWindow() {
      let oAutoSaveTime = window.localStorage.getItem('oAutoSaveTime');
      if (oAutoSaveTime) {
        // 读取缓存设置 ，格式化
        oAutoSaveTime = JSON.parse(oAutoSaveTime);
        if (oAutoSaveTime.closeImgSwitch) {
          // 关闭图像浏览器tab页
          closeWindow();
          return
        }
        return
      }
      closeWindow()
    },

    getCustomTableRowClass({ row, rowIndex }) {
      // 高亮当前患者的那一行
      const sId = this.patientInfo && this.patientInfo.sId
      if (sId) {
        return row.sId === sId ? ' current-row ' : ''
      }
      return ''
    },

    async reloadReportEditPage() {
      const compRef = (this.$refs.reportEditPageCompRef)
      if (compRef.isEditContent) {
        const isConfirmed = await this.$confirm(
          '报告未保存，是否保存报告？',
          '报告未保存',
          {
            confirmButtonText: '保存',
            cancelButtonText: '不保存',
            type: 'warning',
          }
        ).then(() => true)
          .catch(() => false)
        if (isConfirmed) {
          await compRef.onSave(false)
        }
      }

      await compRef.unlockReport(true)
      await compRef.queryCaseReport(true)
    },
    async dblclickInnerTableRow(row) {
      this.toCloseWindow()
      this.openProcess('Report', row)
      await this.reloadReportEditPage() // 报告编辑页中需要更新的部分重新加载
      // this.mxDoSearch()
    },
    getSelectedTableRow({ row }) {
      return row ? 'selected-row' : ''
    },
    openPrintTemplate() {
      this.d_PrintTemplate_v = true;
    },
    closePrintTemplateDialog() {
      this.d_PrintTemplate_v = false;
    },
    // 删除病例
    onBatchDelPatient() {

      if (!this.multipleSelection.length) {
        this.$message.error('请选择要删除的病例！')
        return
      }
      let data = this.multipleSelection.find(item => ![null, undefined, ''].includes(item.sLockUserName))
      if (data) {
        this.$message.error('您选择的病例含有锁定病例，请重新选择！')
        return
      }
      let sId = []
      this.multipleSelection.forEach(item => {
        sId.push(item.sId)
      })
      let nameList = this.multipleSelection.map(item => item.sName);
      this.$confirm(`确定要删除【${nameList.join('、')}】的病例吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        Api.delPatient(sId).then(res => {
          this.loading = false
          if (res.success) {
            this.$message.success(res.msg)
            this.mxDoRefresh()
            return
          }
          this.$message.error(res.msg)
        }).catch(() => {
          console.log(err)
          this.loading = true
        })
      }).catch(() => {

      })

    },
    // 获取树形数据
    getTreeData() {
      this.treeLoading = true;
      caseTypeTree().then(res => {
        this.treeLoading = false;
        this.treeData = [];
        if (res.success) {
          this.treeData = res.data || [];
          return
        }
        this.$message.error(res.msg);
      }).catch(() => {
        this.treeLoading = false;
      })
    },
    handleResetCondition() {
      this.resetCondition()
      this.mxDoSearch()
    },
    async resetCondition() {
      Object.keys(this.condition).forEach(key => (this.condition[key] = null))
      this.condition.iIsImaging = '',
      this.condition.iIsPrint = ''
      this.condition.iIsReportCommit = ''
      this.condition.iIsApprove = ''
      let reportSearchKey = localStorage.getItem('reportSearchKey');
      if (reportSearchKey) {
        this.optionsLoc.recentDayOptions = deepClone(recentDayOptions);
        let target = this.optionsLoc.recentDayOptions.find(item => item.keyWord == reportSearchKey);
        this.condition.sRecentDays = target.keyWord;
        this.condition.dAppointmentTimeSt = moment().add(target.dates[0], 'days').toDate();
        this.condition.dAppointmentTimeEd = moment().add(target.dates[1], 'days').toDate();
      } else {
        this.setDefaultSearchTimes()
      }
      const reportCaseCondition = this.getReportCaseConditionCache();
      if(!Object.keys(reportCaseCondition).length) {
        this.condition.sDistrictId = this.workStation.districtId;
      } else {
        this.condition.sDistrictId = reportCaseCondition.sDistrictId;
        this.condition.sMachineryRoomId = reportCaseCondition.sMachineryRoomId;
      }
      // 重新获取机房数据
      await this.useGetMachineRoomData(this.condition.sDistrictId);
      const sMachineryRoomId = this.condition.sMachineryRoomId;
      if(sMachineryRoomId?.length && Object.prototype.toString.call(sMachineryRoomId) === '[object Array]') {
        // 处理机房多选
        await this.useGetItemData()
        let sDeviceTypeIds = this.optionsLoc.machineRoomArrOption.filter(element => sMachineryRoomId.includes(element.sId)).map(item => item.sDeviceTypeId);
        this.optionsLoc.itemsArrOption = this.optionsLoc.itemsArrOption.filter(item => sDeviceTypeIds.includes(item.sDeviceTypeId));
      } else {
        // 处理机房单选
        var target = this.optionsLoc.machineRoomArrOption.find(element => element.sId === this.condition.sMachineryRoomId);
        target ? this.useGetItemData(target?.sDeviceTypeId) : this.useGetItemData();
      }
    },
    initElementConfigData() {
      const reportSettingStr = window.localStorage.getItem('oAutoSaveTime') || '{}'
      let isShowProcessRecord = false
      try {
        const reportSettingObj = JSON.parse(reportSettingStr)
        isShowProcessRecord = reportSettingObj.isShowProcessRecord > 0
      } catch (error) {
        console.warn(error)
      }
      const ApricotReportForm = CaseReportConfigs.ApricotReportForm.filter(item => {
        return isShowProcessRecord ? true : item.sProp !== 'sProcessRecord'
      })

      this.$store.commit('user/setElementConfigData', {
        elementConfigData: {
          ApricotReportForm,
          ApricotReportEditor1: CaseReportConfigs.ApricotReportEditor1,
          ApricotReportFormEnd: CaseReportConfigs.ApricotReportFormEnd,
          reportTable: Configs.patientTable,
        }
      });
    }

  },
  async mounted() {
    this.initElementConfigData()
    // 查询院区、与院区匹配的机房数据

    await this.useGetHospitalData();
    const Doctors = await getReportAboveDrData()
    this.optionsLoc['sPracticeNameOptions'] = Doctors.practiceDoctors //获取实习医生
    this.optionsLoc['sApproveNameOptions'] = Doctors.auditDoctors //获取审核医生
    this.optionsLoc['sReporterNameOptions'] = Doctors.reportDoctors // 获取报告医生
    this.optionsLoc['sFinalExamineNameOptions'] = Doctors.recheckDoctors // 复审医生

    // 等待表头数据加载完成，再请求表给数据
    // 初始化查询条件
    this.handleResetCondition()
    // this.getTreeData();

    this.switchRetrieveKey()

  },

}
</script>

<style lang="scss" scoped>
.green {
  color: #2cc964;
}

.red {
  color: #f56c6c;
}

.blue {
  color: #308fe8;
}

.c-template {
  position: relative;
}

.c-star {
  position: relative;
  display: inline-block;
  border-radius: 20px;
  text-align: center;

  .i-star {
    position: relative;
    display: inline-block;
    font-size: 22px;
    color: #ffc43e;
    bottom: 1px;
    top: 2px;
  }
}

.batch-audit .el-progress {
  padding-left: 20px;
}

:deep(.el-progress-bar) {
  width: 90%;
}

.batch-audit .audit-status {
  text-align: center;
  padding-bottom: 10px;
  font-size: 18px;
}

.batch-audit .audit-selected {
  padding: 10px 20px;
}

:deep(.el-progress.is-success .el-progress__text) {
  color: #2cc964;
}

.reportTable {
  border-right: 1px solid #ddd;

  :deep(.el-table__body .current-row td.el-table__cell) {
    padding: 10px 0 0 0;
  }
}

:deep(.el-table .el-table__cell.tableColOverflowVisible .cell .row-drawer) {
  padding: 0 0 0 150px;
  left: -62px;
  width: var(--activeTableWidth);
}

:deep(.el-select.no-wrap-select)  {
  .el-select-tags-wrapper{
    display: flex;
    overflow: hidden;
    .el-tag {
        flex: 1;
    }
  }
}
</style>
